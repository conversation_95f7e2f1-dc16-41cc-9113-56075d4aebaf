// triggers/user.ts
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rig<PERSON>, RemoveTrigger } from 'blade/types';

// Trigger for creating users
export const add: AddTrigger = (query) => {
  // Ensure query.with exists
  if (!query.with) {
    return query;
  }

  // Handle both single object and array cases
  const processUserData = (userData: any) => {
    // Set default name if not provided (use email prefix)
    if (!userData.name && userData.email) {
      userData.name = userData.email.split('@')[0];
    } else if (!userData.name) {
      userData.name = 'User';
    }

    // Special handling for students
    if (userData.role === 'student') {
      // Generate username from name if not provided
      if (!userData.username && userData.name) {
        userData.username = userData.name.toLowerCase().replace(/\s+/g, '.');
      }

      // Generate email if not provided
      if (!userData.email && userData.name) {
        userData.email = `${userData.username}@student.school.com`;
      }

      // Use username as slug for students
      if (!userData.slug) {
        if (userData.username) {
          userData.slug = userData.username;
        } else if (userData.email) {
          userData.slug = userData.email.split('@')[0].toLowerCase().replace(/[^a-z0-9-]/g, '') || 'student';
        } else {
          userData.slug = 'student';
        }
      }

      // Set default values for students
      userData.isActive = userData.isActive ?? true;
      userData.emailVerified = false; // Students don't need email verification initially

      // Note: Password is handled by Better Auth through the Account model, not User model

      // Ensure teacherId is set (should be provided when creating student)
      if (!userData.teacherId) {
        console.warn('Student created without teacherId - this may cause issues');
      }
    } else {
      // Auto-generate slug if not provided - prefer name for non-students
      if (!userData.slug) {
        let baseName = 'user';

        if (userData.name) {
          // Use name as base for slug (more user-friendly)
          baseName = userData.name;
        } else if (userData.email) {
          // Fallback to email if no name is provided
          baseName = userData.email.replace('@', '-at-');
        }

        userData.slug = baseName
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '') || 'user';
      }
    }

    // Set default emailVerified if not provided (and not already set for students)
    if (userData.emailVerified === undefined && userData.role !== 'student') {
      userData.emailVerified = false;
    }

    // Set default role if not provided
    if (!userData.role) {
      // For OTP users (those with email), default to teacher
      // Students are created by teachers, so OTP users are always teachers or school_admins
      userData.role = userData.email ? 'teacher' : 'student';
    }

    // Remove any password field that might have been accidentally included
    // Password should only be in the Account model, not User model
    if ('password' in userData) {
      delete userData.password;
      console.log('Removed password field from user data - passwords belong in Account model');
    }

    console.log('User trigger - processed user data:', userData);

    // Set default timestamps
    userData.createdAt = new Date();
    userData.updatedAt = new Date();

    return userData;
  };

  // Handle array of users
  if (Array.isArray(query.with)) {
    query.with = query.with.map(processUserData);
  } else {
    // Handle single user
    query.with = processUserData(query.with);
  }

  return query;
};

// Trigger for updating users
export const set: SetTrigger = (query) => {
  // Ensure query.to exists
  if (!query.to) {
    return query;
  }

  // Update timestamp
  (query.to as any)['updatedAt'] = new Date();

  // Update slug if name is being changed and slug is not already provided
  if ((query.to as any)['name'] && !(query.to as any)['slug']) {
    (query.to as any)['slug'] = (query.to as any)['name']
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'user';
  }

  // Remove any password field that might have been accidentally included
  if ('password' in (query.to as any)) {
    delete (query.to as any).password;
    console.log('Removed password field from user update - passwords belong in Account model');
  }

  // Remove any fields that don't exist in the User model schema
  const validUserFields = [
    'email', 'emailVerified', 'image', 'name', 'username', 'displayUsername',
    'slug', 'role', 'isActive', 'createdAt', 'updatedAt',
    // Student-specific fields
    'teacherId', 'grade', 'classId',
    // Teacher-specific fields
    'isIndependent', 'schoolId', 'department', 'subjects', 'isVerified',
    // School admin-specific fields
    'schoolName', 'schoolAddress', 'schoolPlaceId', 'schoolType', 'schoolDistrict',
    'studentCount', 'teacherCount'
  ];

  const toUpdate = query.to as any;
  const invalidFields = Object.keys(toUpdate).filter(field => !validUserFields.includes(field));

  if (invalidFields.length > 0) {
    console.log('Removing invalid fields from user update:', invalidFields);
    invalidFields.forEach(field => delete toUpdate[field]);
  }

  console.log('User update trigger - processed data:', query.to);

  return query;
};

// Trigger for getting users (can be used for access control)
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};

// Trigger for removing users
export const remove: RemoveTrigger = (query) => {
  // Add any validation or cleanup logic for deletions
  console.log('User removal trigger called with query:', query);
  return query;
};

// Trigger to run after user creation (synchronous)
export const afterAdd = async (query: any, _multiple: any, _options: any) => {
  // Log student creation for debugging
  if (query.with?.role === 'student') {
    console.log('Student created successfully:', {
      name: query.with.name,
      email: query.with.email,
      username: query.with.username,
      slug: query.with.slug,
      teacherId: query.with.teacherId
    });

    // Send invitation email for students - ensure we have all required fields
    if (query.with.email && query.with.teacherId && (query.with.username || query.with.name)) {
      try {
        // Ensure we have a complete student data object
        const studentData = {
          name: query.with.name || 'Student',
          email: query.with.email,
          username: query.with.username || query.with.email.split('@')[0],
          slug: query.with.slug,
          teacherId: query.with.teacherId,
          role: 'student'
        };

        console.log('Sending invitation email for new student:', studentData);
        await sendStudentInvitationEmail(studentData);
      } catch (error) {
        console.error('Failed to send student invitation email:', error);
        // Don't fail the user creation if email fails
      }
    } else {
      console.log('Student created but missing required fields for email:', {
        hasEmail: !!query.with.email,
        hasTeacherId: !!query.with.teacherId,
        hasUsername: !!query.with.username,
        hasName: !!query.with.name
      });
    }

    // Here you could return additional queries to run after student creation:
    // 1. Create default student profile records
    // 2. Create class enrollment records
    // 3. Create notification records for teachers

    // For now, we don't need additional queries, so return empty array
  }

  // Return empty array since we don't need additional queries for now
  return [];
};

// Trigger to run after user update (synchronous)
export const afterSet = async (query: any, _multiple: any, _options: any) => {
  console.log('🔥 afterSet trigger called with query.to:', query.to);

  // Note: Student invitation emails are now handled directly in the API endpoint
  // This trigger is kept for any future user update logic

  return [];
};

// Trigger to run after user removal (synchronous)
export const afterRemove = (query: any, _multiple: any, _options: any) => {
  console.log('🗑️ afterRemove trigger called - user removed successfully');

  // Here you could add cleanup logic:
  // 1. Remove related records (assignments, grades, etc.)
  // 2. Send notification emails to teachers
  // 3. Archive user data instead of hard delete

  // For now, just log the removal
  return [];
};

// Helper function to send student invitation emails
async function sendStudentInvitationEmail(studentData: any) {
  console.log('📧 Starting email send process for:', studentData.email);

  const { resend, validateEmailConfig } = await import('../lib/resend');
  const { StudentInvitationEmail } = await import('../components/emails/student-invitation');
  const { render } = await import('@react-email/render');

  // Validate email configuration
  const emailValidation = validateEmailConfig();
  if (!emailValidation.isValid) {
    console.error('❌ Email configuration error:', emailValidation.error);
    return;
  }

  console.log('✅ Email configuration is valid');

  // For now, we'll use a placeholder teacher name since we don't have easy access to teacher data
  // TODO: Improve this by either passing teacher data or making a query
  const teacherName = 'Your Teacher'; // Placeholder

  // Generate the login URL
  const baseUrl = process.env['BLADE_BETTER_AUTH_URL'] || 'http://localhost:3000';
  const loginUrl = `${baseUrl}/login?role=student`;

  // Render the email template
  const emailComponent = StudentInvitationEmail({
    studentName: studentData.name,
    teacherName: teacherName,
    username: studentData.username || studentData.email.split('@')[0],
    email: studentData.email,
    classes: [], // TODO: Add classes when implemented
    loginUrl,
    password: studentData.password || 'student123' // Use the dynamic password
  });

  const emailHtml = await render(emailComponent as any);

  // Send the email
  console.log('📤 Sending email to:', studentData.email);
  const result = await resend.emails.send({
    from: 'Penned <<EMAIL>>',
    to: studentData.email,
    subject: `Welcome to Penned - Your Student Account is Ready!`,
    html: emailHtml,
  });

  if (result.error) {
    console.error('❌ Resend API error:', result.error);
    throw new Error(`Failed to send email: ${result.error.message}`);
  }

  console.log('✅ Student invitation email sent successfully to:', studentData.email);
  console.log('📧 Email ID:', result.data?.id);
}